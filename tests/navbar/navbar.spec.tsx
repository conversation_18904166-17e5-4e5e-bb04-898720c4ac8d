import { render, screen } from "@testing-library/react";
import { MagnifierIcon } from "@lifesg/react-icons/magnifier";
import { Navbar } from "src/navbar";

describe("Navbar", () => {
    describe("Basic functions", () => {
        it("should render the items (desktop and mobile) if specified", () => {
            const rendered = render(
                <Navbar
                    items={{
                        desktop: MOCK_ITEMS(),
                        mobile: MOCK_ITEMS(),
                    }}
                />
            );

            const { getByTestId } = rendered;

            expect(getByTestId("link__1")).toBeInTheDocument();
            expect(getByTestId("link__2")).toBeInTheDocument();
            expect(getByTestId("link__mobile-1")).toBeInTheDocument();
            expect(getByTestId("link__mobile-2")).toBeInTheDocument();
        });

        it("should render the mobile items even if mobile items are not specified", () => {
            const rendered = render(
                <Navbar items={{ desktop: MOCK_ITEMS() }} />
            );

            const { getByTestId } = rendered;

            expect(getByTestId("link__1")).toBeInTheDocument();
            expect(getByTestId("link__2")).toBeInTheDocument();
            expect(getByTestId("link__mobile-1")).toBeInTheDocument();
            expect(getByTestId("link__mobile-2")).toBeInTheDocument();
        });

        it("should render the indicator on the correct item when clicked", () => {
            const rendered = render(
                <Navbar
                    items={{
                        desktop: MOCK_ITEMS(),
                    }}
                    selectedId="first"
                />
            );

            const { getByTestId } = rendered;
            expect(getByTestId("link__1-indicator")).toBeInTheDocument();
        });

        it("should render the primary brand", () => {
            const rendered = render(
                <Navbar items={{ desktop: MOCK_ITEMS() }} />
            );

            const { getByTestId } = rendered;
            expect(getByTestId("main__brand")).toBeInTheDocument();
        });

        it("should render the secondary brand if specified", () => {
            const rendered = render(
                <Navbar
                    items={{
                        desktop: MOCK_ITEMS(),
                    }}
                    resources={{
                        secondary: {
                            brandName: "Another",
                            logoSrc:
                                "https://assets.life.gov.sg/lifesg/logo-lifesg.svg",
                        },
                    }}
                />
            );

            const { getByTestId } = rendered;
            expect(getByTestId("main__brand-secondary")).toBeInTheDocument();
        });

        it("should not render the links and mobile menu button if hideNavElements is set to true", () => {
            const rendered = render(
                <Navbar items={{ desktop: MOCK_ITEMS() }} hideNavElements />
            );

            const { queryByTestId } = rendered;
            expect(queryByTestId("link__1")).not.toBeInTheDocument();
            expect(
                queryByTestId("button__mobile-menu")
            ).not.toBeInTheDocument();
        });

        describe("mobile menu button", () => {
            it.each`
                scenario                             | desktopItems | mobileItems  | desktopButtons                   | mobileButtons
                ${"there are no items"}              | ${[]}        | ${[]}        | ${[]}                            | ${[]}
                ${"desktop buttons are uncollapsed"} | ${[]}        | ${undefined} | ${[MOCK_UNCOLLAPSIBLE_BUTTON()]} | ${undefined}
                ${"mobile buttons are uncollapsed"}  | ${[]}        | ${undefined} | ${[MOCK_COLLAPSIBLE_BUTTON()]}   | ${[MOCK_UNCOLLAPSIBLE_BUTTON()]}
            `(
                "should hide the mobile menu button given $scenario",
                ({
                    desktopItems,
                    mobileItems,
                    desktopButtons,
                    mobileButtons,
                }) => {
                    render(
                        <Navbar
                            items={{
                                desktop: desktopItems,
                                mobile: mobileItems,
                            }}
                            actionButtons={{
                                desktop: desktopButtons,
                                mobile: mobileButtons,
                            }}
                        />
                    );

                    expect(
                        screen.queryByTestId("button__mobile-menu")
                    ).not.toBeInTheDocument();
                }
            );

            it.each`
                scenario                           | desktopItems    | mobileItems     | desktopButtons                   | mobileButtons
                ${"there are desktop items"}       | ${MOCK_ITEMS()} | ${undefined}    | ${[]}                            | ${undefined}
                ${"there are mobile items"}        | ${[]}           | ${MOCK_ITEMS()} | ${[]}                            | ${undefined}
                ${"desktop buttons are collapsed"} | ${[]}           | ${undefined}    | ${[MOCK_COLLAPSIBLE_BUTTON()]}   | ${undefined}
                ${"mobile buttons are collapsed"}  | ${[]}           | ${undefined}    | ${[MOCK_UNCOLLAPSIBLE_BUTTON()]} | ${[MOCK_COLLAPSIBLE_BUTTON()]}
            `(
                "should show the mobile menu button given $scenario",
                ({
                    desktopItems,
                    mobileItems,
                    desktopButtons,
                    mobileButtons,
                }) => {
                    render(
                        <Navbar
                            items={{
                                desktop: desktopItems,
                                mobile: mobileItems,
                            }}
                            actionButtons={{
                                desktop: desktopButtons,
                                mobile: mobileButtons,
                            }}
                        />
                    );

                    expect(
                        screen.queryByTestId("button__mobile-menu")
                    ).toBeInTheDocument();
                }
            );
        });
    });

    describe("Icon accessibility", () => {
        it("should add aria-hidden to icon-only navbar items", () => {
            render(
                <Navbar
                    items={{
                        desktop: [
                            {
                                id: "search",
                                children: <MagnifierIcon data-testid="desktop-search-icon" />,
                                "aria-label": "Search"
                            }
                        ],
                        mobile: [
                            {
                                id: "search",
                                children: <MagnifierIcon data-testid="mobile-search-icon" />,
                                "aria-label": "Search"
                            }
                        ]
                    }}
                />
            );

            const desktopSearchIcon = screen.getByTestId("desktop-search-icon");
            const mobileSearchIcon = screen.getByTestId("mobile-search-icon");

            expect(desktopSearchIcon).toHaveAttribute("aria-hidden", "true");
            expect(mobileSearchIcon).toHaveAttribute("aria-hidden", "true");
        });

        it("should not modify icons that already have aria-hidden", () => {
            render(
                <Navbar
                    items={{
                        desktop: [
                            {
                                id: "search",
                                children: <MagnifierIcon data-testid="desktop-search-icon" aria-hidden="false" />,
                                "aria-label": "Search"
                            }
                        ],
                        mobile: [
                            {
                                id: "search",
                                children: <MagnifierIcon data-testid="mobile-search-icon" aria-hidden="false" />,
                                "aria-label": "Search"
                            }
                        ]
                    }}
                />
            );

            const desktopSearchIcon = screen.getByTestId("desktop-search-icon");
            const mobileSearchIcon = screen.getByTestId("mobile-search-icon");

            expect(desktopSearchIcon).toHaveAttribute("aria-hidden", "false");
            expect(mobileSearchIcon).toHaveAttribute("aria-hidden", "false");
        });

        it("should not add aria-hidden to text content", () => {
            render(
                <Navbar
                    items={{
                        desktop: [
                            {
                                id: "home",
                                children: "Home"
                            }
                        ]
                    }}
                />
            );

            const homeLink = screen.getByTestId("link__1");
            expect(homeLink).not.toHaveAttribute("aria-hidden");
        });

        it("should warn in development when icon-only content lacks aria-label", () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
            const originalEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'development';

            render(
                <Navbar
                    items={{
                        desktop: [
                            {
                                id: "search",
                                children: <MagnifierIcon data-testid="search-icon" />
                                // Missing aria-label
                            }
                        ]
                    }}
                />
            );

            expect(consoleSpy).toHaveBeenCalledWith(
                'Navbar item with icon-only content should include an aria-label for accessibility. ' +
                'Example: { id: "search", children: <SearchIcon />, "aria-label": "Search" }'
            );

            consoleSpy.mockRestore();
            process.env.NODE_ENV = originalEnv;
        });
    });
});

// =============================================================================
// MOCKS
// =============================================================================
function MOCK_ITEMS() {
    return [
        {
            id: "first",
            children: "First",
        },
        {
            id: "second",
            children: "Second",
        },
    ];
}

function MOCK_UNCOLLAPSIBLE_BUTTON() {
    return {
        type: "button",
        args: { children: "Collapsible" },
        uncollapsible: true,
    };
}

function MOCK_COLLAPSIBLE_BUTTON() {
    return {
        type: "button",
        args: { children: "Uncollapsible" },
    };
}
