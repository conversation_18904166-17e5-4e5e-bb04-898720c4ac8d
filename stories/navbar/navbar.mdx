import { <PERSON><PERSON>, <PERSON>a } from "@storybook/blocks";
import * as NavbarStories from "./navbar.stories";
import { PropsTable } from "./props-table";

<Meta of={NavbarStories} />

# Navbar

## Overview

A component that displays navigation links, action buttons and a brand at the top of the web page.

```tsx
import { Navbar } from "@lifesg/react-design-system/navbar";
```

<Canvas of={NavbarStories.Default} />

## Anatomy

The Navbar comes with 3 main components:

-   Masthead
-   Brand
-   Navigation links

In the mobile viewports, the navigation links are presented in a drawer and is triggered by clicking on the menu icon button.

> **NOTE**: The `Navbar` drawer dismissal animation takes **550ms**. If you are intending to perform scrolling effects in page after a selection in the drawer, you can add a timeout before performing the scrolling

<br />

## Usage examples

## Single action button

This is a common use case among web apps where it requires a Logout button on the navigation bar.

<Canvas of={NavbarStories.SingleActionButton} />

## Multiple action buttons

> **Note**: In the mobile view, the `download` action button will always be positioned last

<Canvas of={NavbarStories.MultipleActionButtons} />

## Custom component action buttons

You can also choose to render a custom component of your choice as an action button.

<Canvas of={NavbarStories.CustomActionButtons} />

## Uncollapsible action buttons

You can set an action button to be uncollapsible, so it remains visible in the navigation bar on mobile viewports.

<Canvas of={NavbarStories.UncollapsibleActionButtons} />

## Custom component items

You can also choose to render a custom component of your choice as a navigation bar item.

<Canvas of={NavbarStories.CustomItems} />

## Prevent dismissal of drawer on specific actions

In this example, we are preventing the drawer's dismissal on the brand logo click. We'll make use of
the `blockDrawerDismissalMethods` prop to do just that.

> **Note**: You will need to view this in a mobile or tablet viewport to see the interaction

<Canvas of={NavbarStories.PreventDrawerDismissal} />

## Sub menu

A sub menu is shown if the selected nav item has sublinks.

<Canvas of={NavbarStories.SubMenu} />

## Secondary branding

In this example, a co-brand is displayed next to the primary brand.

<Canvas of={NavbarStories.SecondaryBranding} />

## Hidden masthead

In this example, the masthead is hidden.

<Canvas of={NavbarStories.HiddenMasthead} />

## Stretched layout

In this example, the content of the navbar is stretched to the full width of
the screen with a fixed padding, ignoring the masthead's alignment.

<Canvas of={NavbarStories.StretchedLayout} />

## Hide branding

When the branding elements are hidden, navigation items will be aligned to the left on desktop.

<Canvas of={NavbarStories.HiddenBranding} />

## Icon accessibility

When using icons as navbar items (such as a search icon), it's important to provide proper accessibility attributes so screen readers can understand the purpose of the link.

<Canvas of={NavbarStories.IconAccessibility} />

**Important:** When using icon-only navbar items, always include an `aria-label` attribute to describe the purpose of the link. The component will automatically add `aria-hidden="true"` to the icon to prevent screen readers from reading it as an image.

```tsx
{
    id: "search",
    children: <MagnifierIcon />,
    "aria-label": "Search" // Required for accessibility
}
```

## Troubleshooting

> The component also uses a custom script. Should you encounter a content security
> policy error, do add this script in the `scriptSrc` blob.
> https://cdn.jsdelivr.net/npm/@govtechsg/sgds-web-component/Masthead/index.js

<br />

## Component API

<PropsTable />
